package com.zhixianghui.web.employer.controller.merchant;

import com.zhixianghui.common.statics.enums.merchant.IdCardTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.merchant.vo.EmployerOperatorVO;
import com.zhixianghui.facade.trade.vo.FreelanceStatVo;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.annotation.CurrentOperatorVO;
import com.zhixianghui.web.employer.biz.data.MerchantInfoAnalyzeBiz;
import com.zhixianghui.web.employer.vo.merchant.DataAnalyzeVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021/7/27 14:19
 */
@Slf4j
@RestController
@RequestMapping("analyze")
public class MerchantInfoAnalyzeController {

    private static final String[] FREELANCE_SORT_COLUMN = {"orderItemNetAmount"};
    public static final String[] ORDER = {"asc", "desc"};
    public static final Map<String, Object> SORT_MAP = new HashMap<>();
    static {
        SORT_MAP.put("orderItemNetAmount", "ORDER_ITEM_NET_AMOUNT");
        SORT_MAP.put("orderItemAmount", "ORDER_ITEM_AMOUNT");
        SORT_MAP.put("receiverNumber", "RECEIVER_NUMBER");
        SORT_MAP.put("orderAmount", "ORDER_AMOUNT");
    }

    @Autowired
    private MerchantInfoAnalyzeBiz analyzeBiz;

    private void checkParam(DataAnalyzeVo analyzeVo) {
        LimitUtil.notEmpty(analyzeVo.getIdCard(), "身份证不能为空");
        LimitUtil.notEmpty(analyzeVo.getMainstayNo(), "代征主体编号不能为空");
        LimitUtil.notEmpty(analyzeVo.getEmployerNo(), "用工企业编号不能为空");
        LimitUtil.notEmpty(analyzeVo.getReceiveName(), "姓名不能为空");

    }

    @RequestMapping("freelanceList")
    public RestResult<PageResult<List<FreelanceStatVo>>> freelanceList(@RequestBody DataAnalyzeVo analyzeVo, @CurrentMchNo String mchNo) {
        String sortColumn = checkSort(analyzeVo, FREELANCE_SORT_COLUMN);
        return RestResult.success(analyzeBiz.freelanceStat(analyzeVo, sortColumn, mchNo));
    }

    private String checkSort(DataAnalyzeVo analyzeVo, String[] sortColumnArray) {
        String sortColumn = analyzeVo.getSortColumn();
        if (StringUtils.isBlank(sortColumn) || StringUtils.isBlank(analyzeVo.getOrder())) {
            return null;
        }

        if (Arrays.asList(ORDER).contains(analyzeVo.getOrder()) &&
                Arrays.asList(sortColumnArray).contains(analyzeVo.getSortColumn())) {
           return SORT_MAP.get(analyzeVo.getSortColumn()) + " " + analyzeVo.getOrder();
        }
        throw CommonExceptions.PARAM_INVALID.newWithErrMsg("排序字段或规则不合法");
    }

    @RequestMapping("countFreelance")
    public RestResult<FreelanceStatVo> countFreelance(@RequestBody DataAnalyzeVo analyzeVo, @CurrentMchNo String mchNo) {
        return RestResult.success(analyzeBiz.countFreelance(analyzeVo, mchNo));
    }


    @RequestMapping("sign")
    public RestResult<String> sign(@RequestBody DataAnalyzeVo analyzeVo) {
        checkParam(analyzeVo);
        if (analyzeBiz.sign(analyzeVo)) {
            return RestResult.success("发起签约成功");
        }
        return RestResult.error("该用户发放时未传手机号码, 请补充手机号码");
    }

    @RequestMapping("uploadIdCard")
    public RestResult<String> uploadIdCard(@RequestBody DataAnalyzeVo analyzeVo) {
        if (!Objects.isNull(analyzeVo.getIdCardType())){
            if (analyzeVo.getIdCardType().intValue() == IdCardTypeEnum.ORIGINAL.getCode().intValue()) {
                LimitUtil.notEmpty(analyzeVo.getIdCardBackUrl(), "身份证背面地址不能为空");
                LimitUtil.notEmpty(analyzeVo.getIdCardFrontUrl(), "身份证正面地址不能为空");
            }else {
                LimitUtil.notEmpty(analyzeVo.getIdCardCopyFileUrl(), "身份证背面地址不能为空");
            }
        }


        LimitUtil.notEmpty(analyzeVo.getId(), "缺少参数id");

        if (analyzeBiz.uploadIdCard(analyzeVo)) {
            return RestResult.success("上传成功");
        }
        return RestResult.error("上传失败");
    }

}
