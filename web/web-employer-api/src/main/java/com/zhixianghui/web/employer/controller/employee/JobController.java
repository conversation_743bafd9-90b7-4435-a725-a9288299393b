package com.zhixianghui.web.employer.controller.employee;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.enums.sign.SignStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.facade.employee.dto.*;
import com.zhixianghui.facade.employee.entity.Job;
import com.zhixianghui.facade.employee.service.JobFacade;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.service.SignRecordFacade;
import com.zhixianghui.facade.trade.service.UserInfoFacade;
import com.zhixianghui.facade.trade.vo.SignMemberVo;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.annotation.CurrentStaffVo;
import com.zhixianghui.web.employer.biz.employee.TestJobMemberListener;
import com.zhixianghui.web.employer.dto.JobMemberUploadDto;
import com.zhixianghui.web.employer.utils.MultipartFileUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("job")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class JobController {
    private static final int FILE_SIZE = 5;
    private static final String UNIT = "M";
    private static final String SUFFIX = ".xlsx";
    private static final int HEAD_ROW_NUMBER = 2;
    @Reference(retries = -1)
    private JobFacade jobFacade;
    @Reference
    private UserInfoFacade userInfoFacade;
    @Reference
    private SignRecordFacade signRecordFacade;

    /**
     * @decribe 新增任务
     * @param jobDto
     * @return
     */
    @PostMapping("add")
    public RestResult<Job> addJob(@Validated @RequestBody JobDto jobDto, @CurrentStaffVo  EmployerStaffVO staffVo) {
        jobDto.setEmployerNo(staffVo.getMchNo());
        jobDto.setEmployerName(staffVo.getMchName());
        final Job job = jobFacade.addJob(jobDto);
        return RestResult.success(job);
    }

    @PostMapping("page")
    public RestResult<IPage> pageJobs(@RequestBody Page page, @RequestBody JobQueryDto jobQueryDto, @CurrentMchNo String mchNo) {
        jobQueryDto.setEmployerNo(mchNo);
        final Page<Job> jobPage = jobFacade.pageJob(page, BeanUtil.toMap(jobQueryDto));
        return RestResult.success(jobPage);
    }

    @PostMapping("getJobList")
    public RestResult getJobList(@RequestBody JobWebQueryDto jobWebQueryDto,@CurrentMchNo String mchNo) {
        jobWebQueryDto.setEmployerNo(mchNo);
        List<Job> list = jobFacade.list(jobWebQueryDto);
        List<HashMap<String, String>> data = list.stream().map(item -> {
            HashMap<String, String> map = new HashMap();
            map.put("jobId", item.getJobId());
            map.put("jobName", item.getJobName());
            map.put("workCategoryCode", item.getWorkCategoryCode());
            map.put("workCategoryName", item.getWorkCategoryName());
            return map;
        }).collect(Collectors.toList());
        return RestResult.success(data);
    }

    @PostMapping("listJobListOnGrant")
    public RestResult<List<Job>> listJobListOnGrant(@CurrentMchNo String mchNo) {
        Map<String, Object> param = new HashMap<>();
        param.put("employerNo", mchNo);
        final List<Job> jobList = jobFacade.listJobListOnGrant(param);
        return RestResult.success(jobList);
    }
    @GetMapping("/getById/{id}")
    public RestResult getJobById(@PathVariable("id") Long id) {
        return RestResult.success(jobFacade.getJobById(id));
    }

    @GetMapping("/getAppQrCode/{id}")
    public RestResult getAppQrCode(@PathVariable("id")Long id){
        return RestResult.success(jobFacade.getQrCode(id));
    }

    @GetMapping("getByJobId")
    public RestResult<Job> getByJobId(@RequestParam String jobId) {
        final Job job = jobFacade.getJobByJobId(jobId);
        return RestResult.success(job);
    }

    @PostMapping("pageJob")
    public RestResult<IPage<Job>> pageJob(@RequestBody Page page, @RequestBody JobQueryDto jobQueryDto,@CurrentMchNo String mchNo) {
        jobQueryDto.setEmployerNo(mchNo);
        final Page<Job> jobPage = jobFacade.pageJob(page, jobQueryDto);
        return RestResult.success(jobPage);
    }

    @PostMapping("uploadJobMember")
    public RestResult uploadJobMember(@Validated JobMemberUploadDto uploadDto) {

        final MultipartFile file = uploadDto.getFile();
        if(!MultipartFileUtil.checkFileSize(file.getSize(), FILE_SIZE, UNIT)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件大小超过限制");
        }

        if(!file.getOriginalFilename().endsWith(SUFFIX)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("FileName:" + file.getOriginalFilename() +"文件格式有误，必须为xlsx格式");
        }
        try {
            File excelFile = MultipartFileUtil.transfer2File(file);
            List<JobMemberExcelDto> list = new ArrayList<>();
            EasyExcel.read(excelFile, JobMemberExcelDto.class, new TestJobMemberListener(HEAD_ROW_NUMBER, list)).sheet().headRowNumber(HEAD_ROW_NUMBER).doRead();

            if (CollectionUtils.isEmpty(list) || emptyContent(list)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("提交的发放名单不能为空");
            }
            jobFacade.uploadJobMember(uploadDto.getId(),excelFile.getName(), FileUtils.file2byte(excelFile));

            return RestResult.success("正在导入数据，请稍后刷新数据");
        } catch (Exception e) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("解析Excel文件出错");
        }
    }

    @GetMapping("/getSignedMembers")
    public RestResult<List<SignMemberVo>> getSignedMembers(@CurrentMchNo String mchNo) {

        Map<String, Object> param = new HashMap<>();
        param.put("employerNo", mchNo);
        param.put("signStatus", SignStatusEnum.SIGN_SUCCESS.getValue());

        final List<SignRecord> signRecords = signRecordFacade.listRecords(param);
        if (signRecords == null) {
            return RestResult.success(new ArrayList<>());
        }

        Map<String, String> map = new HashMap<>();

        final List<SignMemberVo> signMemberVos = signRecords.stream().map(member -> {
            SignMemberVo signMemberVo = new SignMemberVo();
            signMemberVo.setName(member.getReceiveNameDecrypt());
            signMemberVo.setPhone(member.getReceivePhoneNoDecrypt());
            signMemberVo.setIdCard(member.getReceiveIdCardNoDecrypt());
            signMemberVo.setEmployerNo(member.getEmployerNo());
            if (StringUtils.isNotBlank(member.getReceivePhoneNoDecrypt())) {
                map.put(member.getReceiveIdCardNoDecrypt(), member.getReceivePhoneNoDecrypt());
            }
            return signMemberVo;
        }).distinct().collect(Collectors.toList());

        for (SignMemberVo memberVo : signMemberVos) {
            if (StringUtils.isBlank(memberVo.getPhone()) && StringUtils.isNotBlank(map.get(memberVo.getIdCard()))) {
                memberVo.setPhone(map.get(memberVo.getIdCard()));
            }
        }

        final List<SignMemberVo> collect = signMemberVos.stream().distinct().collect(Collectors.toList());

        return RestResult.success(collect);
    }

    @PostMapping("/addSignedMember")
    private RestResult<String> addSignedMember(@Valid @RequestBody SignedJobMemberDto memberDto) {

        jobFacade.uploadJobMember(memberDto);
        return RestResult.success("已指派人员到任务，请切到列表查看");
    }

    @PostMapping("/delete/{id}")
    public RestResult delete(@PathVariable("id")Long id){
        jobFacade.deleteById(id);
        return RestResult.success("删除成功");
    }

    @PostMapping("/update")
    public RestResult update(@RequestBody JobDto jobDto){
        return RestResult.success(jobFacade.updateById(jobDto));
    }

    @PostMapping("/completeJob/{id}")
    public RestResult completeJob(@PathVariable("id")Long id){
        jobFacade.completeJob(id);
        return RestResult.success("操作成功");
    }

    private boolean emptyContent(List<JobMemberExcelDto> list) {
        for (JobMemberExcelDto item : list) {
            if (StringUtils.isNotBlank(item.getWorkerPhone())) {
                return false;
            }
        }
        return true;
    }
}
