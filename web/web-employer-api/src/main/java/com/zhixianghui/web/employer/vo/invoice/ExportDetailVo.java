package com.zhixianghui.web.employer.vo.invoice;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ExportDetailVo {

    @ExcelProperty(value = "平台批次号")
    private String platBatchNo;
    @ExcelProperty(value = "平台流水号")
    private String platTrxNo;
    @ExcelProperty(value = "姓名")
    private String receiveNameDecrypt;
    @ExcelProperty(value = "身份证号")
    private String receiveIdCardNoDecrypt;
    @ExcelProperty(value = "收款账号")
    private String receiveAccountNoDecrypt;
    @ExcelProperty(value = "手机号")
    private String receivePhoneNoDecrypt;
    @ExcelProperty(value = "实发金额")
    private BigDecimal orderItemNetAmount;
    @ExcelProperty(value = "税费")
    private String orderItemTaxAmount;
    @ExcelProperty(value = "服务费")
    private BigDecimal orderItemFee;
}
