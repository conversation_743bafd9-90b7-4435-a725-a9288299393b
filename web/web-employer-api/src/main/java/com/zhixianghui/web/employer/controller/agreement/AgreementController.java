package com.zhixianghui.web.employer.controller.agreement;

import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.export.FileTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.AgreementFileTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.AgreementStatusEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.DesensitizeUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.Agreement;
import com.zhixianghui.facade.merchant.entity.AgreementFile;
import com.zhixianghui.facade.merchant.entity.AgreementSigner;
import com.zhixianghui.facade.merchant.service.AgreementFacade;
import com.zhixianghui.facade.merchant.service.AgreementFileFacade;
import com.zhixianghui.facade.merchant.service.AgreementSignerFacade;
import com.zhixianghui.facade.merchant.vo.AgreementQuerysVo;
import com.zhixianghui.facade.merchant.vo.AgreementResVo;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.annotation.CurrentStaffVo;
import com.zhixianghui.web.employer.utils.BeanToMapUtil;
import com.zhixianghui.web.employer.vo.PageVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName AgreementController
 * @Description TODO
 * @Date 2022/8/18 18:10
 */
@RestController
@RequestMapping("agreement")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgreementController {

    @Reference
    private AgreementFacade agreementFacade;
    @Reference
    private AgreementSignerFacade agreementSignerFacade;
    @Reference
    private AgreementFileFacade agreementFileFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;

    /**
     * 导出归档文件
     * @return
     */
    @PostMapping("exportFile")
    public RestResult exportFile(@RequestBody String idListStr, @CurrentStaffVo EmployerStaffVO staffVO){
        JSONObject json = JSON.parseObject(idListStr);
        JSONArray idArray = json.getJSONArray("idList");
        if(idArray == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("参数idList为空");
        }
        List<Long> idList = idArray.toJavaList(Long.class);

        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("exportFileType", FileTypeEnum.ZIP.getValue());
        if (idList == null || idList.size() < 0){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请选中所需导出归档文件");
        }
        paramMap.put("agreementIdList",idList);
        paramMap.put("type", AgreementFileTypeEnum.ARCHIVE.getValue());
        //先校验一下是否包含归档文件
        List<Agreement> agreementList = agreementFacade.listBy(paramMap);
        if (agreementList.size() == 0 || agreementList.stream().noneMatch(x->x.getStatus().intValue() == AgreementStatusEnum.FINISHED.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("所选数据没有已完成的协议");
        }
        ExportRecord record = ExportRecord.newDefaultInstance();
        String fileNo = exportRecordFacade.genFileNo();
        record.setFileNo(fileNo);
        record.setMchNo(staffVO.getMchNo());
        record.setOperatorLoginName(staffVO.getPhone());
        record.setSystemType(SystemTypeEnum.MERCHANT_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.AGREEMENT_PORTAL_EXPORT.getFileName());
        record.setReportType(ReportTypeEnum.AGREEMENT_PORTAL_EXPORT.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    /**
     * 获取签约地址
     * @param id
     * @param mchNo
     * @return
     */
    @GetMapping("getSignUrl/{id}")
    public RestResult getSignUrl(@PathVariable Long id, @CurrentMchNo String mchNo){
        Agreement agreement = agreementFacade.getAgreementById(id);
        if (agreement == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("协议不存在");
        }
        AgreementSigner agreementSigner = agreementSignerFacade.getBySignNoAndId(mchNo,id);
        if (agreementSigner == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("签署人不存在");
        }
        return RestResult.success(agreementSigner.getSignUrl());
    }

    @PostMapping("getAgreementPage")
    public RestResult getAgreementPage(@RequestBody AgreementQuerysVo agreementQueryVo, @RequestBody(required=false) PageVo pageVo,@CurrentMchNo String mchNo){
        Map<String, Object> paramMap = BeanToMapUtil.beanToMap(agreementQueryVo);
        List<String> signerNoList = new ArrayList<String>(){{add(mchNo);}};
        if (!CollectionUtils.isEmpty(agreementQueryVo.getSignerNoList())){
            signerNoList.addAll(agreementQueryVo.getSignerNoList());
        }
        paramMap.put("signerNoList",signerNoList);
        PageResult<List<Agreement>> pageResult = agreementFacade.listPageByMerchant(paramMap,pageVo.toPageParam());
        List<AgreementResVo> list = pageResult.getData().stream().map(
                agreement -> {
                    AgreementResVo agreementResVo = new AgreementResVo();
                    BeanUtils.copyProperties(agreement,agreementResVo);
                    agreement.setSalerId(null);
                    agreement.setSalerName(null);
                    //查询协议方
                    List<AgreementSigner> signerList = agreementSignerFacade.listByAgreementId(agreement.getId());
                    signerList.stream().forEach(x->x.setSignerPhone(DesensitizeUtil.handleMobile(x.getSignerPhone())));
                    //查询协议文件
                    List<AgreementFile> fileList = agreementFileFacade.listByAgreementId(agreement.getId());
                    agreementResVo.setSignerList(signerList);
                    agreementResVo.setFileList(fileList);
                    return agreementResVo;
                }
        ).collect(Collectors.toList());
        return RestResult.success(PageResult.newInstance(list, pageResult.getPageCurrent(), pageResult.getPageSize(), pageResult.getTotalRecord()));
    }
}
