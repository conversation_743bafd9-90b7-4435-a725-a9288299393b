package com.zhixianghui.web.employer.controller.merchant;


import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantQuoteStatusEnum;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition;
import com.zhixianghui.facade.merchant.entity.MerchantInvoiceInfo;
import com.zhixianghui.facade.merchant.service.MerchantEmployerPositionFacade;
import com.zhixianghui.facade.merchant.service.MerchantInvoiceInfoFacade;
import com.zhixianghui.facade.merchant.vo.InvoiceCategoryVo;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.annotation.Logger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Author: huangshunsi
 * Date: 2020.12.25
 * Time: 11:56
 * Description:
 */
@Slf4j
@RequestMapping("merchantInvoiceInfo")
@RestController
public class MerchantInvoiceInfoController {

    @Reference
    private MerchantInvoiceInfoFacade merchantInvoiceInfoFacade;
    @Reference
    private MerchantEmployerPositionFacade merchantEmployerPositionFacade;

    @RequestMapping("getInvoiceInfoWithMainstayNo")
    public RestResult getInvoiceInfoWithMainstayNo(@CurrentMchNo String mchNo,@RequestParam String mainstayNo,@RequestParam(required = false) String workCategoryCode){
        MerchantInvoiceInfo invoiceInfo = merchantInvoiceInfoFacade.getByMchNo(mchNo);
        LimitUtil.notEmpty(invoiceInfo, "商户开票信息不存在");

        Map<String, Object> resultMap = BeanUtil.toMap(invoiceInfo);
        List<InvoiceCategoryVo> invoiceCategoryVoList = new ArrayList<>();
        // 获取所有绑定的发票类目信息
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("mchNo",mchNo);
        paramMap.put("workCategoryCode",workCategoryCode);
        paramMap.put("status", MerchantQuoteStatusEnum.ACTIVE.getValue());
        List<MerchantEmployerPosition> positionList = merchantEmployerPositionFacade.listByMchNoWithQuote(paramMap);
        if (CollectionUtils.isNotEmpty(positionList)) {
            positionList.forEach(x -> {
                invoiceCategoryVoList.addAll(x.getJsonEntity().getInvoiceCategoryList());
            });
        }
        // 去重
        List<InvoiceCategoryVo> uniqueInvoiceCategoryVoList = invoiceCategoryVoList.stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(InvoiceCategoryVo::getInvoiceCategoryCode))), ArrayList::new)
        );
        resultMap.put("invoiceCategoryVoList", uniqueInvoiceCategoryVoList);
        return RestResult.success(resultMap);
    }

    /**
     * 获取商户开票信息
     *
     * @return .
     */
    @RequestMapping("getInvoiceInfo")
    public RestResult<Map<String, Object>> getInvoiceInfo(@CurrentMchNo String mchNo) {
        MerchantInvoiceInfo invoiceInfo = merchantInvoiceInfoFacade.getByMchNo(mchNo);
        LimitUtil.notEmpty(invoiceInfo, "商户开票信息不存在");

        Map<String, Object> resultMap = BeanUtil.toMap(invoiceInfo);
        List<InvoiceCategoryVo> invoiceCategoryVoList = new ArrayList<>();
        // 获取所有绑定的发票类目信息
        List<MerchantEmployerPosition> positionList = merchantEmployerPositionFacade.listByMchNo(mchNo);
        if (CollectionUtils.isNotEmpty(positionList)) {
            positionList.forEach(x -> {
                invoiceCategoryVoList.addAll(x.getJsonEntity().getInvoiceCategoryList());
            });
        }
        // 去重
        List<InvoiceCategoryVo> uniqueInvoiceCategoryVoList = invoiceCategoryVoList.stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(InvoiceCategoryVo::getInvoiceCategoryCode))), ArrayList::new)
        );
        resultMap.put("invoiceCategoryVoList", uniqueInvoiceCategoryVoList);
        return RestResult.success(resultMap);
    }

    /**
     * 更新商户开票信息
     *
     * @return .
     */
    @RequestMapping("updateInvoiceInfo")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "更新商户开票信息")
    @Permission("merchant:invoiceInfo:update")
    public RestResult<String> updateInvoiceInfo(@CurrentMchNo String mchNo) {
        MerchantInvoiceInfo invoiceInfo = merchantInvoiceInfoFacade.getByMchNo(mchNo);
        LimitUtil.notEmpty(invoiceInfo, "商户开票信息不存在");

        merchantInvoiceInfoFacade.update(invoiceInfo);
        return RestResult.success("更新成功");
    }
}
