package com.zhixianghui.web.employer.vo.merchant;

import com.zhixianghui.web.employer.vo.PageVo;
import lombok.Data;
import java.io.Serializable;


/**
 * <AUTHOR>
 * @Date 2021/7/27 14:35
 */
@Data
public class DataAnalyzeVo extends PageVo implements Serializable {

    private static final long serialVersionUID = 685183002837823214L;
    private String beginDate;
    private String endDate;
    private String currentDate;
    private String mainstayNo;
    private String mainstayName;
    private String employerNo;
    private String employerName;
    private String receiveName;
    private String idCard;
    private String receiveIdCardNoMd5;
    private String receiveNameMd5;
    private String phone;
    private Long signId;
    private Long id;
    private String sortColumn;
    private String order;
    /**
     * @see com.zhixianghui.common.statics.enums.merchant.IdCardTypeEnum
     */
    private Integer idCardType;
    private String idCardBackUrl;
    private String idCardFrontUrl;
    /**
     * 身份证复印件url
     */
    private String idCardCopyFileUrl;
    private String cerFaceUrl;
    private String bankCardNumber;
    private Integer hasUploadIdCard;

    private Integer signType;

    private String platTrxNo;
}
