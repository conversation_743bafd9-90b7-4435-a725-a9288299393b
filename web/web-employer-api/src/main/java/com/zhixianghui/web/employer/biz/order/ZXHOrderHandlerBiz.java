package com.zhixianghui.web.employer.biz.order;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.event.AnalysisEventListener;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.merchant.vo.EmployerOperatorVO;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.web.employer.biz.order.excel.BankGrantRow;
import com.zhixianghui.web.employer.biz.order.excel.CKHGrantDataListener;
import com.zhixianghui.web.employer.biz.order.excel.GrantDataListener;
import com.zhixianghui.web.employer.vo.order.CountVo;
import com.zhixianghui.web.employer.vo.order.req.OrderAcceptReqVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName ZXHOrderHandlerBiz
 * @Description TODO
 * @Date 2022/7/4 11:34
 */
@Slf4j
@Service
public class ZXHOrderHandlerBiz extends AbstractOrderHandler{
    @Override
    public Map<String, String> orderUpload(OrderAcceptReqVo orderAcceptReqVo, EmployerOperatorVO operator, String employerNo) throws Exception {
        return this.batchOrderUpload(orderAcceptReqVo,operator,employerNo);
    }

    @Override
    public String getBatchNo(String employerNo, Date batchTime) {
        //生成订单
        String batchId = sequenceFacade.nextRedisId("", SequenceBizKeyEnum.ORDER_SEQ.getKey(), SequenceBizKeyEnum.ORDER_SEQ.getWidth());
        String batchNo = SequenceBizKeyEnum.ORDER_SEQ.getPrefix()+ DateUtil.formatCompactDate(batchTime) + batchId;
        log.info("[{}]==>生成订单批次号:{}", employerNo, batchNo);
        return batchNo;
    }

    @Override
    public void handleExcel(File excelFile, Order order, CountVo countVo) {
        EasyExcel.read(excelFile, BankGrantRow.class, this.getListener(order,countVo)).sheet().headRowNumber(HEAD_ROW_NUMBER).doRead();
        //汇总信息
        order.setRequestCount(countVo.getRequestCount());
        //默认任务金额=实发金额
        order.setRequestNetAmount(countVo.getRequestNetAmount());
        order.setRequestTaskAmount(countVo.getRequestNetAmount());
        order.setFailCount(countVo.getFailCount());
        order.setFailTaskAmount(countVo.getFailNetAmount());
    }

    private AnalysisEventListener<BankGrantRow> getListener(Order order, CountVo countVo) {
        return new GrantDataListener(orderItemBiz,sequenceFacade,notifyFacade,order,countVo,HEAD_ROW_NUMBER,
                this.phoneMustMainstays);
    }
}
