package com.zhixianghui.web.employer.controller.trade;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.common.SuccessFailEnum;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.service.joinpay.JoinPayFacade;
import com.zhixianghui.facade.banklink.vo.withdraw.WithdrawQueryVo;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.trade.constant.JoinPayConstant;
import com.zhixianghui.facade.trade.service.OrderFacade;
import com.zhixianghui.facade.trade.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年01月04日 10:20:00
 */
@RestController
@RequestMapping("/withdraw")
@Slf4j
public class WithdrawController {

    @Reference
    private OrderFacade orderFacade;
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Reference
    private JoinPayFacade joinPayFacade;

    @RequestMapping("/bank/callback")
    public String bankCallback(@RequestBody Map<String, Object> map) {
        log.info("提现请求回调内容 : {}", JSONObject.toJSONString(map));
        updateOrder(map);
        return JSONObject.toJSONString(new ResponseVo());
    }

    private void updateOrder(Map<String, Object> map){
        String dataStr = JsonUtil.toString(map.get("data"));
        Map<String,Object> data=JsonUtil.toBean(dataStr,Map.class);
        if(data.get("biz_code").equals(JoinPayConstant.SUCCESS_BIZ_CODE)&&
            data.get("order_status").equals(JoinPayConstant.SUCCESS_ORDER_CODE)){
            orderFacade.updateWithdrawRecord(String.valueOf(data.get("mch_order_no")), SuccessFailEnum.SUCCESS,"");
        }else{
            orderFacade.updateWithdrawRecord(
                    String.valueOf(data.get("mch_order_no")), SuccessFailEnum.FAIL,
                    String.valueOf(data.get("biz_msg")));
        }
    }


    @PostMapping("/bank/query")
    public RestResult bankQuery(@RequestBody WithdrawQueryVo withdrawQueryVo) {
        JSONObject query = joinPayFacade.query(withdrawQueryVo);
        log.info("提现请求回调内容 : {}", JSONObject.toJSONString(query));
        String data = query.toJSONString();
        updateOrder(JsonUtil.toBean(data,Map.class));
        return RestResult.success("回查成功");
    }
}