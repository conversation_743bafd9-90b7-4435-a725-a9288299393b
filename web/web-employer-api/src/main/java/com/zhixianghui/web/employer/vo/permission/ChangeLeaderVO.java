package com.zhixianghui.web.employer.vo.permission;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

@Data
public class ChangeLeaderVO {

    /**
     * 新负责人手机号
     */
    @NotEmpty(message = "手机号不能为空")
    @Pattern(regexp = "^1[0-9]{10}$", message = "手机号不正确")
    private String newLeaderPhone;

    /**
     * 新负责人姓名
     */
    @NotEmpty(message = "新负责人姓名不能为空")
    private String newLeaderName;

    /**
     * 短信验证码
     */
    @NotEmpty(message = "短信验证码不能为空")
    private String smsCode;

    /**
     * 支付密码
     */
    @NotEmpty(message = "支付密码不能为空")
    private String tradePwd;
}
