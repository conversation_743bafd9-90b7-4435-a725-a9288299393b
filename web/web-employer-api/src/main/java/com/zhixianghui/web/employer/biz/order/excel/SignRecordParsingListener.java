package com.zhixianghui.web.employer.biz.order.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.zhixianghui.common.statics.enums.sign.SignSmsEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.ValidateUtil;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.trade.vo.SignRecordVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/4/25 11:39
 */
@Slf4j
public class SignRecordParsingListener extends AnalysisEventListener<SignRecordVo> {

    private final int headRowNumber;
    private final Integer sms;
    private List<SignRecordVo> list;
    private DataDictionaryFacade dataDictionaryFacade;

    public SignRecordParsingListener(int headRowNumber, List<SignRecordVo> signRecordVoList, DataDictionaryFacade dataDictionaryFacade, Integer sms) {
        this.headRowNumber = headRowNumber;
        this.list = signRecordVoList;
        this.dataDictionaryFacade = dataDictionaryFacade;
        this.sms = sms;
    }




    @Override
    public void invoke(SignRecordVo data, AnalysisContext context) {
        if (data == null) {
            return;
        }
        Integer rowNum = context.readRowHolder().getRowIndex();
        checkParam(data, rowNum);
        list.add(data);
    }

    /**
     * 校验每个参数
     * @param data 签约数据
     * @param rowNum 第几行
     */
    private void checkParam(SignRecordVo data, Integer rowNum) {
        if (StringUtils.isBlank(data.getIdCard())) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("[解析文件业务异常]-身份证号不能为空");
        }
        if (StringUtils.isBlank(data.getName())) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("[解析文件业务异常]-名称不能为空");
        }
        if (!ValidateUtil.isChineseName(data.getName())) {
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("[解析文件业务异常]-name  姓名(" + data.getName() + ")格式错误");
        }
        // 2022-05-06 修改为只有在需要发送短信的模式下才校验手机号
        if (sms == SignSmsEnum.SEND.getStatus()) {
            if (StringUtils.isBlank(data.getPhone())) {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("[解析文件业务异常]-短信通知模式手机号码不能为空");
            }
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        log.info("所有数据解析完成！");
    }

}
