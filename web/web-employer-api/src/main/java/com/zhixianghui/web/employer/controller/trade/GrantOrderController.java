package com.zhixianghui.web.employer.controller.trade;

import com.zhixianghui.common.statics.enums.merchant.IdCardTypeEnum;
import com.zhixianghui.common.statics.enums.sign.ChannelSignTypeEnum;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.web.employer.annotation.CurrentStaffVo;
import com.zhixianghui.web.employer.biz.data.MerchantInfoAnalyzeBiz;
import com.zhixianghui.web.employer.vo.merchant.DataAnalyzeVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月26日 14:20:00
 */
@RestController
@RequestMapping("/grant")
@Slf4j
public class GrantOrderController {


    @Autowired
    private MerchantInfoAnalyzeBiz analyzeBiz;
    @Reference
    private OrderItemFacade orderItemFacade;

    @PostMapping("/rejectOrderItem/{platTrxNo}")
    public RestResult<String> rejectOrderItem(@PathVariable("platTrxNo") String platTrxNo, @CurrentStaffVo EmployerStaffVO staffVo) {
        orderItemFacade.rejectOrderItem(platTrxNo,staffVo.getOperatorName());
        return RestResult.success("取消成功");
    }

    @PostMapping("/signByOrderItem/{platTrxNo}")
    public RestResult<String> signByOrderItem(@PathVariable("platTrxNo") String platTrxNo) {
        DataAnalyzeVo analyzeVo = new DataAnalyzeVo();
        OrderItem orderItem = orderItemFacade.getByPlatTrxNo(platTrxNo);
        log.info("发起签约：{}", orderItem);
        analyzeVo.setIdCard(orderItem.getReceiveIdCardNoDecrypt());
        analyzeVo.setEmployerNo(orderItem.getEmployerNo());
        analyzeVo.setMainstayNo(orderItem.getMainstayNo());
        analyzeVo.setReceiveName(orderItem.getReceiveNameDecrypt());
        analyzeVo.setMainstayName(orderItem.getMainstayName());
        analyzeVo.setEmployerName(orderItem.getEmployerName());
        if (StringUtils.isNoneBlank(orderItem.getReceivePhoneNoDecrypt())) {
            analyzeVo.setSignType(ChannelSignTypeEnum.MSG.getValue());
            analyzeVo.setPhone(orderItem.getReceivePhoneNoDecrypt());
        } else {
            analyzeVo.setSignType(ChannelSignTypeEnum.URL_NO_CODE.getValue());
        }
        checkParam(analyzeVo);
        if (analyzeBiz.signOrder(analyzeVo)) {
            return RestResult.success("发起签约成功");
        }
        return RestResult.error("该用户发放时未传手机号码, 请补充手机号码");
    }

    private void checkParam(DataAnalyzeVo analyzeVo) {
        LimitUtil.notEmpty(analyzeVo.getSignType(), "签约方式不能为空");
        LimitUtil.notEmpty(analyzeVo.getIdCard(), "身份证不能为空");
        LimitUtil.notEmpty(analyzeVo.getMainstayNo(), "代征主体编号不能为空");
        LimitUtil.notEmpty(analyzeVo.getEmployerNo(), "用工企业编号不能为空");
        LimitUtil.notEmpty(analyzeVo.getReceiveName(), "姓名不能为空");
        if (analyzeVo.getSignType().intValue() == ChannelSignTypeEnum.MSG.getValue() || analyzeVo.getSignType().intValue() == ChannelSignTypeEnum.URL_CODE.getValue()) {
            LimitUtil.notEmpty(analyzeVo.getPhone(), "手机号不能为空");
        }
    }


    @RequestMapping("uploadIdCard")
    public RestResult<String> uploadIdCard(@RequestBody DataAnalyzeVo analyzeVo) {
        if (!Objects.isNull(analyzeVo.getIdCardType())) {
            if (analyzeVo.getIdCardType().intValue() == IdCardTypeEnum.ORIGINAL.getCode().intValue()) {
                LimitUtil.notEmpty(analyzeVo.getIdCardBackUrl(), "身份证背面地址不能为空");
                LimitUtil.notEmpty(analyzeVo.getIdCardFrontUrl(), "身份证正面地址不能为空");
            } else {
                LimitUtil.notEmpty(analyzeVo.getIdCardCopyFileUrl(), "身份证背面地址不能为空");
            }
        }


        LimitUtil.notEmpty(analyzeVo.getPlatTrxNo(), "缺少订单编号");

        if (analyzeBiz.uploadIdCardOrder(analyzeVo)) {
            return RestResult.success("上传成功");
        }
        return RestResult.error("上传失败");
    }
}