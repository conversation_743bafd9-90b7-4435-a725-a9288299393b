package com.zhixianghui.web.employer.vo.sign;

import lombok.Data;

import java.io.Serializable;

@Data
public class AddSignImagesVo implements Serializable {
    private static final long serialVersionUID = 2242037755085257782L;

    /**
     * 签约ID
     */
    private Long signId;

    /**
     * 身份证正面url
     */
    private String idCardFrontUrl;

    /**
     * 身份证背面
     */
    private String idCardBackUrl;

    private String idCardCopyFileUrl;
    /**
     * 手写签名
     */
    private String signatureUrl;

    /**
     * 半身照url
     */
    private String cerFaceUrl;

    /**
     * 身份证类型
     * @see com.zhixianghui.common.statics.enums.merchant.IdCardTypeEnum
     */
    private Integer idCardType;
}
