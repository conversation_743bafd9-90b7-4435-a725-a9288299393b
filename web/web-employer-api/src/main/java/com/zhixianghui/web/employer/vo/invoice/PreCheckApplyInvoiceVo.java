package com.zhixianghui.web.employer.vo.invoice;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class PreCheckApplyInvoiceVo {
    /**
     * 代征主体编号
     */
    @NotBlank(message = "代征主体编号 不能为空")
    private String mainstayMchNo;

    /**
     * 岗位类目编号
     */
    @NotBlank(message = "岗位类目编号 不能为空")
    private String workCategoryCode;

    /**
     * 产品编号
     */
    @NotBlank(message = "产品编号 不能为空")
    private String productNo;
}
