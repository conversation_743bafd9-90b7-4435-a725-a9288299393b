package com.zhixianghui.web.employer.controller.trade;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.fee.entity.Product;
import com.zhixianghui.facade.fee.service.ProductFunctionFacade;
import com.zhixianghui.web.employer.vo.merchant.ProductFunctionQueryVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("product_function_manager")
public class ProductFunctionController{

    @Reference
    private ProductFunctionFacade facade;

    @PostMapping("list")
    public RestResult<PageResult<List<Product>>> listProductFunction(@RequestBody @Valid ProductFunctionQueryVo vo) {
        PageResult<List<Product>> pageResult = facade.getProductFunction(BeanUtil.toMap(vo), PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize(),"UPDATE_TIME DESC"));
        return RestResult.success(pageResult);
    }

    @GetMapping("listAll")
    public RestResult<List<Product>> listProductFunction() {
        List<Product> pageResult = facade.getAllProductFunction();
        return RestResult.success(pageResult);
    }

}
