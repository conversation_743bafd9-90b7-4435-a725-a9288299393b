package com.zhixianghui.web.employer.controller.flow;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.flow.dto.CommonFlowLogVo;
import com.zhixianghui.facade.flow.dto.CommonFlowVo;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.service.FlowImageFacade;
import com.zhixianghui.facade.flow.vo.req.*;
import com.zhixianghui.facade.flow.vo.res.TaskResVo;
import com.zhixianghui.facade.merchant.service.employer.EmployerRoleFacade;
import com.zhixianghui.facade.merchant.service.employer.EmployerStaffFacade;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.web.employer.annotation.CurrentStaffVo;
import com.zhixianghui.web.employer.vo.PageVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName FlowController
 * @Description TODO
 * @Date 2021/5/17 9:16
 */
@RestController
@RequestMapping("flow")
public class FlowController extends CommonFlow {

    @Reference
    private FlowFacade flowFacade;

    @Reference
    private FlowImageFacade flowImageFacade;

    @Reference
    private EmployerStaffFacade employerStaffFacade;

    @Reference
    private EmployerRoleFacade employerRoleFacade;

    /**
     * 我的已办
     *
     * @param paramMap
     * @param pageVo
     * @param staffVO
     * @return
     */
    @Permission("merchant:flow:view")
    @PostMapping("handleList")
    public RestResult<PageResult<List<Map<String,Object>>>> handleList(@RequestBody Map<String, Object> paramMap, @RequestBody PageVo pageVo, @CurrentStaffVo EmployerStaffVO staffVO) {
        boolean admin = admin(staffVO, employerRoleFacade);
        paramMap.put("mchNo", staffVO.getMchNo());
        PageResult<List<Map<String,Object>>> taskList = flowFacade.handleList(staffVO.getId(), PlatformSource.MERCHANT.getValue(), admin, paramMap, pageVo.toPageParam());
        return RestResult.success(taskList);
    }

    /**
     * 我的发起
     *
     * @param paramMap
     * @param pageVo
     * @param staffVO
     * @return
     */
    @Permission("merchant:flow:view")
    @PostMapping("sendList")
    public RestResult<PageResult<List<Map<String,Object>>>> sendList(@RequestBody Map<String, Object> paramMap, @RequestBody PageVo pageVo, @CurrentStaffVo EmployerStaffVO staffVO) {
        boolean admin = admin(staffVO, employerRoleFacade);
        paramMap.put("mchNo", staffVO.getMchNo());
        PageResult<List<Map<String,Object>>> taskList = flowFacade.sendList(staffVO.getId(), PlatformSource.MERCHANT.getValue(), admin, paramMap, pageVo.toPageParam());
        return RestResult.success(taskList);
    }

    /**
     * 我的待办
     *
     * @param paramMap
     * @param pageVo
     * @param staffVO
     * @return
     */
    @Permission("merchant:flow:view")
    @PostMapping("todoList")
    public RestResult<PageResult<List<Map<String,Object>>>> todoList(@RequestBody Map<String, Object> paramMap, @RequestBody PageVo pageVo, @CurrentStaffVo EmployerStaffVO staffVO) {
        PageResult<List<Map<String,Object>>> taskList = flowFacade.todoList(getFlowUserVo(staffVO), false, paramMap, pageVo.toPageParam());
        return RestResult.success(taskList);
    }

    /**
     * 获取操作日志
     *
     * @param commonFlowId 流程通用id
     * @return
     */
    @Permission("merchant:flow:view")
    @GetMapping("getDetail")
    public RestResult<List<CommonFlowLogVo>> getDetail(@RequestParam(name = "commonFlowId") Long commonFlowId) {
        List<CommonFlowLogVo> commonFlowLogList = flowFacade.getDetailByCommonFlowId(commonFlowId, PlatformSource.MERCHANT.getValue());
        return RestResult.success(commonFlowLogList);
    }

    /**
     * 根据流程实例id获取流程追踪图
     *
     * @param processInstanceId 流程实例id
     * @return
     * @throws IOException
     */
    @Permission("merchant:flow:image")
    @GetMapping("getInstanceImage")
    public RestResult<?> getInstanceImage(@RequestParam(name = "processInstanceId") String processInstanceId) throws IOException {
        String base64 = flowImageFacade.getInstanceImage(processInstanceId);
        return RestResult.success(base64);
    }

    /**
     * 提交任务
     *
     * @param taskHandleVo
     * @return
     */
    @Permission("merchant:flow:submit")
    @PostMapping("submitTask")
    public RestResult<?> submitTask(@RequestBody TaskHandleVo taskHandleVo, @CurrentStaffVo EmployerStaffVO staffVO) {
        flowFacade.executeTask(taskHandleVo, getFlowUserVo(staffVO), false);
        return RestResult.success("任务提交完毕");
    }

    @Permission("merchant:flow:submit")
    @PostMapping("withDraw")
    public RestResult<?> withDraw(@RequestBody Map<String, Object> map, @CurrentStaffVo EmployerStaffVO staffVO) {
        Long commonFlowId = Long.valueOf(String.valueOf(map.get("commonFlowId")));
        String reason = (String) map.get("reason");
        flowFacade.deleteProcessInstance(commonFlowId, getFlowUserVo(staffVO), false, reason);
        return RestResult.success("流程撤回成功");
    }

    /**
     * 获取流程数据
     *
     * @param commonFlowId
     * @return
     */
    @Permission("merchant:flow:view")
    @GetMapping("getByCommonFlowId")
    public RestResult<CommonFlowVo> getByCommonFlowId(@RequestParam("commonFlowId") Long commonFlowId, @RequestParam(value = "taskId", required = false) String taskId,
                                                      @CurrentStaffVo EmployerStaffVO staffVO) {
        return RestResult.success(flowFacade.getCommonFlowById(commonFlowId, taskId, getFlowUserVo(staffVO), false, PlatformSource.MERCHANT.getValue()));
    }

    @Permission("merchant:flow:submit")
    @PostMapping("editBusinessVariable")
    public RestResult<?> editBusinessVariable(@RequestBody @Validated(IEditBusinessVariable.class) CommonFlowEditVo commonFlowEditVo, @CurrentStaffVo EmployerStaffVO staffVO) {
        flowFacade.editBusinessVariable(commonFlowEditVo, getFlowUserVo(staffVO), false);
        return RestResult.success("修改信息成功");
    }

    @Permission("merchant:flow:submit")
    @PostMapping("transferTask")
    public RestResult<?> transferTask(@RequestBody @Validated(ITransferUser.class) CommonFlowEditVo commonFlowEditVo, @CurrentStaffVo EmployerStaffVO staffVO) {
        //获取变更人
        EmployerStaffVO employerStaffVO = employerStaffFacade.getById(staffVO.getMchNo(), commonFlowEditVo.getNextUserId());
        if (employerStaffVO == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用户不存在，请选择其他用户");
        }
        FlowUserVo nextUser = new FlowUserVo();
        nextUser.setUserId(employerStaffVO.getId());
        nextUser.setPlatform(PlatformSource.MERCHANT.getValue());
        nextUser.setUserName(employerStaffVO.getName());
        nextUser.setNo(employerStaffVO.getMchNo());
        flowFacade.transferTask(commonFlowEditVo, getFlowUserVo(staffVO), nextUser);
        return RestResult.success("变更审批人成功");
    }

    @Permission("merchant:flow:reply")
    @PostMapping("reply")
    public RestResult<?> reply(@RequestBody @Validated(IReply.class) CommonFlowEditVo commonFlowEditVo, @CurrentStaffVo EmployerStaffVO employerStaffVO) {
        flowFacade.reply(commonFlowEditVo, getFlowUserVo(employerStaffVO));
        return RestResult.success("回复成功");
    }


    private FlowUserVo getFlowUserVo(EmployerStaffVO staffVO) {
        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setPlatform(PlatformSource.MERCHANT.getValue());
        flowUserVo.setUserId(staffVO.getId());
        flowUserVo.setUserName(StringUtil.isEmpty(staffVO.getName()) ? staffVO.getMchNo() : staffVO.getName());
        flowUserVo.setNo(staffVO.getMchNo());
        return flowUserVo;
    }
}
