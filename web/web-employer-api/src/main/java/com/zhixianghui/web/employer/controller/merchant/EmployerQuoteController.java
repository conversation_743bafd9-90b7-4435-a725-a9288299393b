package com.zhixianghui.web.employer.controller.merchant;

import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.merchant.entity.MerchantCkhQuote;
import com.zhixianghui.facade.merchant.service.MerchantCkhQuoteFacade;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @ClassName EmployerQuoteController
 * @Description TODO
 * @Date 2023/1/31 15:59
 */
@Slf4j
@RestController
@RequestMapping("quote")
public class EmployerQuoteController {

    @Reference
    private MerchantCkhQuoteFacade merchantCkhQuoteFacade;

    @GetMapping("/getCkhQuoteByMainstayNo")
    public RestResult getCkhQuoteByMainstayNo(@RequestParam("mainstayNo") String mainstayNo, @CurrentMchNo String mchNo){
        MerchantCkhQuote merchantCkhQuote =  merchantCkhQuoteFacade.getFeeRate(mchNo,mainstayNo, ProductNoEnum.CKH.getValue());
        if (merchantCkhQuote == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("结算信息不存在，请联系产品经理");
        }
        return RestResult.success(merchantCkhQuote);
    }
}
