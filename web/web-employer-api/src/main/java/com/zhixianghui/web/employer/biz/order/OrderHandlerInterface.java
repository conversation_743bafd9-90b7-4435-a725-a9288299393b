package com.zhixianghui.web.employer.biz.order;

import com.alibaba.excel.event.AnalysisEventListener;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.facade.merchant.vo.EmployerOperatorVO;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.web.employer.biz.order.excel.BankGrantRow;
import com.zhixianghui.web.employer.biz.order.excel.GrantListenerInterface;
import com.zhixianghui.web.employer.vo.order.CountVo;
import com.zhixianghui.web.employer.vo.order.req.OrderAcceptReqVo;
import org.apache.poi.ss.formula.functions.Count;

import java.io.File;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName OrderHandlerInterface
 * @Description TODO
 * @Date 2022/6/28 9:55
 */
public interface OrderHandlerInterface {

    /**
     * 限制上传文件大小，单位：M
     */
    int FILE_SIZE = 5;
    String UNIT = "M";
    String SUFFIX = ".xlsx";

    /**
     * excel文件行头数
     */
    int HEAD_ROW_NUMBER = 2;

    Map<String,String> orderUpload(OrderAcceptReqVo orderAcceptReqVo, EmployerOperatorVO operator, String employerNo) throws Exception;

    /**
     * 获取批次数据列表
     * @param employerNo
     * @param batchTime
     * @return
     */
    String getBatchNo(String employerNo, Date batchTime);

    /**
     * 获取Excel处理器
     * @param order
     * @param countVo
     * @return
     */
    void handleExcel(File excelFile, Order order, CountVo countVo);
    //AnalysisEventListener<BankGrantRow> getListener(Order order, CountVo countVo);
}
