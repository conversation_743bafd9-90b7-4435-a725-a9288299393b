package com.zhixianghui.web.employer.controller.invoice;

import cn.hutool.core.io.IoUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.enums.invoice.InvoiceStatusEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoiceTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.trade.dto.InvoiceRecordDetailDto;
import com.zhixianghui.facade.trade.entity.InvoiceRecord;
import com.zhixianghui.facade.trade.entity.InvoiceRecordDetail;
import com.zhixianghui.facade.trade.entity.OfflineOrderItem;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.InvoiceSourceEnum;
import com.zhixianghui.facade.trade.service.InvoiceFacade;
import com.zhixianghui.facade.trade.service.InvoiceRecordDetailFacade;
import com.zhixianghui.facade.trade.service.OfflineOrderItemFacade;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.facade.trade.vo.InvoiceDetailGroupByIdCardVo;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.biz.excel.ExportBiz;
import com.zhixianghui.web.employer.vo.invoice.ExportDetailGroupVo;
import com.zhixianghui.web.employer.vo.invoice.ExportDetailVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
public class InvoiceDownloadController {

    @Autowired
    private ExportBiz exportBiz;

    @Reference
    private InvoiceRecordDetailFacade invoiceRecordDetailFacade;
    @Reference
    private InvoiceFacade invoiceFacade;
    @Reference
    private OrderItemFacade orderItemFacade;
    @Reference
    private OfflineOrderItemFacade offlineOrderItemFacade;

    @Autowired
    private FastdfsClient fastdfsClient;

    @GetMapping("download/exportInvoiceDetail")
    public void exportInvoiceDetail(InvoiceRecordDetailDto invoiceRecordDetailDto, @CurrentMchNo String mchNo, HttpServletResponse servletResponse) {

        final InvoiceRecord invoiceRecord = invoiceFacade.getByTrxNo(invoiceRecordDetailDto.getInvoiceTrxNo());
        if (invoiceRecord == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("发票记录不存在");
        }

        int current = 1;
        int size = 200;
        IPage<InvoiceRecordDetail> detailPage = null;
        List<ExportDetailVo> allData = new LinkedList<>();
        if (invoiceRecord.getSource().intValue() == InvoiceSourceEnum.ON_LINE.getCode().intValue()) {
            do {
                Page<InvoiceRecordDetail> page = new Page<>(current, size);
                detailPage = invoiceRecordDetailFacade.invoiceRecordDetailPage(page, invoiceRecordDetailDto);
                if (detailPage != null && detailPage.getRecords() != null && !detailPage.getRecords().isEmpty()) {
                    allData.addAll(detailPage.getRecords().stream().map(it->{
                        OrderItem orderItem;
                        if (invoiceRecord.getSource().intValue() == InvoiceSourceEnum.ON_LINE.getCode().intValue()) {
                            orderItem = orderItemFacade.getByPlatTrxNo(it.getPlatTrxNo());
                        }else {
                            final OfflineOrderItem item = offlineOrderItemFacade.getOrderItemByPlatTrxNo(it.getPlatTrxNo());
                            orderItem = new OrderItem();
                            BeanUtil.copyProperties(item, orderItem);
                        }
                        ExportDetailVo vo = new ExportDetailVo();
                        BeanUtil.copyProperties(orderItem, vo);
                        return vo;
                    }).collect(Collectors.toList()));
                    current++;
                }
            } while (detailPage != null && detailPage.getRecords() != null && !detailPage.getRecords().isEmpty());
        }

        current = 1;
        IPage<InvoiceDetailGroupByIdCardVo> groupDetailPage = null;
        List<ExportDetailGroupVo> allGroupData = new LinkedList<>();
        do {
            Page<InvoiceDetailGroupByIdCardVo> page = new Page<>(current, size);
            groupDetailPage = invoiceRecordDetailFacade.listInvoiceDetailGroupByIdCard(page, invoiceRecordDetailDto.getInvoiceTrxNo());

            if (groupDetailPage != null && groupDetailPage.getRecords() != null && !groupDetailPage.getRecords().isEmpty()) {
                allGroupData.addAll(
                        groupDetailPage.getRecords().stream().map(it->{
                            ExportDetailGroupVo vo = new ExportDetailGroupVo();
                            vo.setReceiveNameDecrypt(it.getReceiveName());
                            vo.setReceiveAccountNoDecrypt(it.getReceiveAccountNo());
                            vo.setReceivePhoneNoDecrypt(it.getReceivePhoneNo());
                            vo.setReceiveIdCardNoDecrypt(it.getReceiveIdCardNo());
                            vo.setInvoiceAmount(it.getInvoiceAmount());
                            vo.setInvoiceStatus(InvoiceStatusEnum.getEnum(it.getInvoiceStatus()).getDesc());
                            vo.setInvoiceType(InvoiceTypeEnum.getEnum(it.getInvoiceType()).getDesc());
                            vo.setWorkCategoryName(invoiceRecord.getInvoiceCategoryName());
                            return vo;
                        }).collect(Collectors.toList())
                );
                current++;
            }
        } while (groupDetailPage != null && groupDetailPage.getRecords() != null && !groupDetailPage.getRecords().isEmpty());


        exportBiz.export(servletResponse, "开票清单",allGroupData, allData,ExportDetailGroupVo.class, ExportDetailVo.class);

    }

    @GetMapping("download/downloadInvoiceSingle")
    public void downloadInvoiceSingle(@RequestParam(required = true)String invoiceTrxNo, @RequestParam(required = true)  String platTrxNo, HttpServletResponse servletResponse) throws IOException {
        InvoiceRecordDetailDto invoiceRecordDetailDto = new InvoiceRecordDetailDto();
        invoiceRecordDetailDto.setReceiveIdCardNoMd5(MD5Util.getMixMd5Str(platTrxNo));
        invoiceRecordDetailDto.setInvoiceTrxNo(invoiceRecordDetailDto.getInvoiceTrxNo());
        final List<InvoiceRecordDetail> invoiceRecordDetails = invoiceRecordDetailFacade.listInvoiceRecordDetailByInvoiceTrxNoAndIdCardNo(invoiceTrxNo,platTrxNo);
        final String tempPath = genRandomPath();

        FileInputStream inputStream = null;
        ServletOutputStream out = servletResponse.getOutputStream();
        try {
            for (InvoiceRecordDetail invoiceRecordDetail : invoiceRecordDetails) {
                final List<String> fileUrls = invoiceRecordDetail.getInvoiceFileUrl();
                for (String fileUrl : fileUrls) {
                    String suffix = fileUrl.substring(fileUrl.lastIndexOf("/"));
                    File invioceFile = FileUtils.createFile(tempPath + File.separator + invoiceRecordDetail.getPlatTrxNo() + File.separator + suffix);
                    final InputStream invoiceInputStream = fastdfsClient.downloadFile(fileUrl);
                    org.apache.commons.io.FileUtils.copyInputStreamToFile(invoiceInputStream, invioceFile);
                }
            }
            File zipFile = ZipUtil.zipFileKeepConstruct(tempPath);
            servletResponse.setContentType("application/vnd.ms-excel");
            servletResponse.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(zipFile.getName(), "UTF-8").replaceAll("\\+", "%20");
            servletResponse.setHeader("Content-disposition", "attachment;filename=" + fileName);

            inputStream = new FileInputStream(zipFile);

            IoUtil.copy(inputStream, out);
            out.flush();
        } finally {
            if (out != null) {
                out.close();
            }
            if (inputStream != null) {
                inputStream.close();
            }
            FileUtils.deleteDir(new File(tempPath));
        }
    }

    @GetMapping("download/downloadInvoiceBatch")
    public void downloadInvoiceBatch(@RequestParam(required = true)  String invoiceTrxNo, HttpServletResponse servletResponse) throws IOException {
        final String tempPath = genRandomPath();
        FileInputStream inputStream = null;
        ServletOutputStream out = servletResponse.getOutputStream();
        final List<InvoiceRecordDetail> invoiceRecordDetails = invoiceRecordDetailFacade.listInvoiceRecordDetailByInvoiceTrxNo(invoiceTrxNo);
        try {
            for (InvoiceRecordDetail invoiceRecordDetail : invoiceRecordDetails) {
                final List<String> fileUrls = invoiceRecordDetail.getInvoiceFileUrl();
                if (fileUrls == null || fileUrls.isEmpty()) {
                    continue;
                }
                final String platTrxNo = invoiceRecordDetail.getPlatTrxNo();
                for (String fileUrl : fileUrls) {
                    String suffix = fileUrl.substring(fileUrl.lastIndexOf("/"));
                    File invioceFile = FileUtils.createFile(tempPath + File.separator + platTrxNo + File.separator + suffix);
                    final InputStream invoiceInputStream = fastdfsClient.downloadFile(fileUrl);
                    org.apache.commons.io.FileUtils.copyInputStreamToFile(invoiceInputStream, invioceFile);
                }
            }
            File zipFile = ZipUtil.zipFileKeepConstruct(tempPath);
            servletResponse.setContentType("application/vnd.ms-excel");
            servletResponse.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(zipFile.getName(), "UTF-8").replaceAll("\\+", "%20");
            servletResponse.setHeader("Content-disposition", "attachment;filename=" + fileName);

            inputStream = new FileInputStream(zipFile);

            IoUtil.copy(inputStream, out);
            out.flush();
        }finally {
            if (out != null) {
                out.close();
            }
            if (inputStream != null) {
                inputStream.close();
            }
            FileUtils.deleteDir(new File(tempPath));
        }
    }

    private String genRandomPath() {
        try {
            String filePath = System.getProperty("user.dir")
                    + File.separator
                    + "export"
                    + File.separator
                    + RandomUtil.get16LenStr();
            FileUtils.creatDir(filePath);
            return filePath;
        } catch (Exception e) {
            throw CommonExceptions.UNEXPECT_ERROR.newWith("创建文件失败", e);
        }

    }
}
