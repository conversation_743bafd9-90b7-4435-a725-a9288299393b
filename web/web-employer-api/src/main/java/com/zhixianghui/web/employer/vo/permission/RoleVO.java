package com.zhixianghui.web.employer.vo.permission;

import com.zhixianghui.common.statics.enums.common.RoleTypeEnum;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerRole;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.Date;

@Data
public class RoleVO {

    /**
     * id
     */
    private Long id;

    /**
     * 角色名称
     */
    @NotEmpty(message = "角色名称不能为空")
    @Size(min = 2, max = 20, message = "角色名称在2到20个字符")
    private String roleName;

    /**
     * 描述
     */
    @Size( max = 50, message = "描述长度不能超过50")
    private String remark;

    public static EmployerRole buildDto(RoleVO vo, String mchNo) {
        EmployerRole role = new EmployerRole();
        role.setId(vo.getId());
        role.setCreateTime(new Date());
        role.setMchNo(mchNo);
        role.setName(vo.getRoleName());
        role.setRemark(vo.getRemark());
        role.setRoleType(RoleTypeEnum.CUSTOMIZE.getType());

        return role;
    }

    public static RoleVO buildVo(EmployerRole role) {
        RoleVO vo = new RoleVO();
        vo.setId(role.getId());
        vo.setRoleName(role.getName());
        vo.setRemark(role.getRemark());
        return vo;
    }
}
