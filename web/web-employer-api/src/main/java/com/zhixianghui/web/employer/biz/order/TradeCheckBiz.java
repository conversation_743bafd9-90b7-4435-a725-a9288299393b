package com.zhixianghui.web.employer.biz.order;

import com.zhixianghui.common.statics.enums.merchant.MchStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName TradeCheckBiz
 * @Description TODO
 * @Date 2022/7/4 11:44
 */
@Slf4j
@Service
public class TradeCheckBiz {

    @Reference
    private MerchantQueryFacade merchantQueryFacade;

    public void checkMchInfo(String employerNo,String mainstayNo) {
        log.info("校验商户状态信息,用工企业编号:{},代征主体编号:{}",employerNo,mainstayNo);
        //商户状态
        Merchant merchant = merchantQueryFacade.getByMchNo(employerNo);
        if (merchant == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("编号[" + employerNo + "] 商户账号不存在,请联系客服");
        }
        if (!merchant.getMchStatus().equals(MchStatusEnum.ACTIVE.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("您的商户状态未激活,请联系客服");
        }
        //代征主体
        Merchant mainstay = merchantQueryFacade.getByMchNo(mainstayNo);
        if (mainstay == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("编号[" + mainstayNo + "] 所选代征主体不存在,请联系客服");
        }
        if (!mainstay.getMchStatus().equals(MchStatusEnum.ACTIVE.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("所选代征主体状态未激活,请联系客服");
        }
    }

    public boolean checkReport(EmployerAccountInfo accountInfo) {
        if (StringUtils.isBlank(accountInfo.getParentMerchantNo())
                ||StringUtils.isBlank(accountInfo.getSubMerchantNo())
                ||StringUtils.isBlank(accountInfo.getParentAgreementNo())
                ||StringUtils.isBlank(accountInfo.getSubAgreementNo())
                ||StringUtils.isBlank(accountInfo.getParentAlipayUserId())
                ||StringUtils.isBlank(accountInfo.getSubAlipayUserId()))
        {
            return false;
        }

        return true;
    }
}
