package com.zhixianghui.web.employer.vo.sign;

import com.zhixianghui.facade.trade.vo.SignRecordVo;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2021/1/18 15:36
 **/
@Data
public class SignRecordQueryVo {
    /**
     * 签约状态
     */
    private Integer signStatus;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 姓名
     */
    private String receiveName;

    /**
     * 身份证号
     */
    private String receiveIdCardNo;

    /**
     * 手机号
     */
    private String receivePhoneNo;

    /**
     * 创建起始时间
     */
    private Date createBeginDate;

    /**
     * 创建截止时间
     */
    private Date createEndDate;

    private Long id;

    public SignRecordVo toSignRecordVo(SignRecordQueryVo queryVo) {
        SignRecordVo signRecordVo = new SignRecordVo();
        signRecordVo.setId(queryVo.getId());
        signRecordVo.setIdCard(queryVo.getReceiveIdCardNo());
        signRecordVo.setName(queryVo.getReceiveName());
        signRecordVo.setPhone(queryVo.getReceivePhoneNo());
        return signRecordVo;
    }

}
