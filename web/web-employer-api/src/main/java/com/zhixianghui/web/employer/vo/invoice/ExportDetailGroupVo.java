package com.zhixianghui.web.employer.vo.invoice;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ExportDetailGroupVo {

    @ExcelProperty(value = "发票类型")
    private String invoiceType;
    @ExcelProperty(value = "开票类目")
    private String workCategoryName;
    @ExcelProperty(value = "姓名")
    private String receiveNameDecrypt;
    @ExcelProperty(value = "身份证号")
    private String receiveIdCardNoDecrypt;
    @ExcelProperty(value = "收款账号")
    private String receiveAccountNoDecrypt;
    @ExcelProperty(value = "手机号")
    private String receivePhoneNoDecrypt;
    @ExcelProperty(value = "开票金额")
    private BigDecimal invoiceAmount;
    @ExcelProperty(value = "状态")
    private String invoiceStatus;

}
