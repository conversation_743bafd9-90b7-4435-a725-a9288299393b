package com.zhixianghui.web.employer.biz.invoice;

import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.AmountUtil;
import com.zhixianghui.facade.employee.service.JobFacade;
import com.zhixianghui.facade.fee.service.MerchantFeeCalculateFacade;
import com.zhixianghui.facade.merchant.entity.MerchantCkhQuote;
import com.zhixianghui.facade.merchant.service.MerchantCkhQuoteFacade;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.facade.trade.dto.OfflineInvoiceApplyDto;
import com.zhixianghui.facade.trade.service.InvoiceFacade;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;

@Service
@Slf4j
public class InvoiceBiz {

    @Reference
    private JobFacade jobFacade;

    @Reference
    private InvoiceFacade invoiceFacade;

    @Autowired
    private FastdfsClient fastdfsClient;

    @Reference
    private MerchantFeeCalculateFacade merchantFeeCalculateFacade;
    @Reference
    private MerchantCkhQuoteFacade merchantCkhQuoteFacade;

    public String applyOffline(OfflineInvoiceApplyDto reqDto, EmployerStaffVO staffVO) throws IOException {
        reqDto.setEmployerNo(staffVO.getMchNo());
        reqDto.setEmployerName(staffVO.getMchName());
        return invoiceFacade.applyOffline(reqDto);
    }

    public BigDecimal calculateServiceFee(String employerNo,String mainstayNo,BigDecimal netAmount) {
        //查看报价规则
        MerchantCkhQuote merchantCkhQuote = merchantCkhQuoteFacade.getFeeRate(employerNo,mainstayNo,ProductNoEnum.CKH.getValue());
        if (merchantCkhQuote == null){
            log.error("找不到对应的报价单：[{}]",employerNo);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("计费异常");
        }
        //计算服务费
        BigDecimal serviceFee = AmountUtil.mul(netAmount, merchantCkhQuote.getServiceFeeRate(), 2);
        //最低费用0.01元
        if (serviceFee.compareTo(BigDecimal.ZERO) != 1){
            serviceFee = new BigDecimal("0.01");
        }
        return serviceFee;
    }
}
