package com.zhixianghui.web.employer.controller.taxcertificate;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.trade.entity.TaxCertificateRecord;
import com.zhixianghui.facade.trade.service.TaxCertificateRecordFacade;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("taxCertificate")
public class TaxCertificateController {

    @Reference
    private TaxCertificateRecordFacade taxCertificateRecordFacade;



    @PostMapping("listPage")
    public RestResult<Page<TaxCertificateRecord>> listPage(@RequestBody Page<TaxCertificateRecord> page, @RequestBody Map<String,Object> param, @CurrentMchNo String mchNo) {
        param.put("employerNo", mchNo);
        return RestResult.success(taxCertificateRecordFacade.listPage(page, param));
    }

}
