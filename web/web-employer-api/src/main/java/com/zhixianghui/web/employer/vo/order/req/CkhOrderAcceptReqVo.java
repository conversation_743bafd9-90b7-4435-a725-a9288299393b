package com.zhixianghui.web.employer.vo.order.req;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description 批次订单请求Vo
 * @date 2020-11-03 16:09
 **/
@Data
public class CkhOrderAcceptReqVo {

    @NotBlank(message = "产品编号不能为空")
    private String productNo;

    private String productName;

    /**
     * 自由职业者服务编号
     */
    @NotEmpty(message = "自由职业者服务编号不能为空")
    private String workCategoryCode;

    /**
     * 自由职业者服务名称
     */
    @NotEmpty(message = "自由职业者服务名称不能为空")
    private String workCategoryName;

    /**
     * 自由职业者服务描述
     */
    @NotEmpty(message = "自由职业者服务描述不能为空")
    private String serviceDesc;

    /**
     * 代征主体编号
     */
    @NotEmpty(message = "代征主体编号不能为空")
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    @NotEmpty(message = "代征主体名称不能为空")
    private String mainstayName;

    /**
     * 任务id
     */
    private String jobId;

    /**
     * 任务名称
     */
    private String jobName;

    /**
     * 上传的文件
     */
    @NotNull(message = "上传的文件不能为空")
    MultipartFile file;

}
