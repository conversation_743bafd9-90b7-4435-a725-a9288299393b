package com.zhixianghui.web.employer.vo.order.res;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-13 16:22
 **/
@Data
public class OrderWithCountResVo {

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 确认发放时间
     */
    private Date confirmTime;

    /**
     * 商户批次号
     */
    private String mchBatchNo;

    /**
     * 批次名
     */
    private String batchName;

    /**
     * 平台批次号
     */
    private String platBatchNo;

    /**
     * 批次状态 {@link com.zhixianghui.facade.trade.enums.OrderStatusEnum}
     */
    private Integer batchStatus;

    /**
     * 请求笔数
     */
    private Integer requestCount;

    /**
     * 请求(总)实发金额
     */
    private BigDecimal requestNetAmount;

    /**
     * 已受理笔数
     */
    private Integer acceptedCount;

    /**
     * 已受理(总)实发金额
     */
    private BigDecimal acceptedNetAmount;

    /**
     * 已受理代征主体服务费
     */
    private BigDecimal acceptedFee;

    /**
     * 已受理(总)订单金额
     */
    private BigDecimal acceptedOrderAmount;

    /**
     * 成功笔数
     */
    private Integer successCount;

    /**
     * 成功实发金额
     */
    private BigDecimal successNetAmount;

    /**
     * 失败笔数
     */
    private Integer failCount;

    /**
     * 失败实发金额
     */
    private BigDecimal failNetAmount;
}
