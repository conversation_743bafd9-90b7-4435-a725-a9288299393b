package com.zhixianghui.web.employer.controller.common;

import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MchStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.user.portal.PortalInitPwdStatusEnum;
import com.zhixianghui.common.statics.enums.user.portal.PortalLoginTypeEnum;
import com.zhixianghui.common.statics.enums.user.portal.PortalOperatorStatusEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.RSAUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerMain;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerOperator;
import com.zhixianghui.facade.merchant.service.MerchantEmployerMainFacade;
import com.zhixianghui.facade.merchant.service.employer.EmployerOperatorFacade;
import com.zhixianghui.facade.merchant.service.employer.EmployerRoleFacade;
import com.zhixianghui.facade.merchant.service.employer.EmployerStaffFacade;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.merchant.vo.EmployerOperatorVO;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.web.employer.annotation.CurrentOperatorVO;
import com.zhixianghui.web.employer.annotation.CurrentSession;
import com.zhixianghui.web.employer.annotation.Logger;
import com.zhixianghui.web.employer.component.*;
import com.zhixianghui.web.employer.constant.PermissionConstant;
import com.zhixianghui.web.employer.dto.JwtInfo;
import com.zhixianghui.web.employer.dto.Session;
import com.zhixianghui.web.employer.utils.NetUtil;
import com.zhixianghui.web.employer.vo.permission.FunctionVO;
import com.zhixianghui.web.employer.vo.user.*;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 公共接口
 */
@RestController
@RequestMapping("user")
@Log4j2
public class UserController {

    @Autowired
    private SessionManager sessionManager;

    @Autowired
    private JwtHelper jwtHelper;

    @Autowired
    private CaptchaHelper captchaHelper;

    @Autowired
    private DataDecryptHelper dataDecryptHelper;

    @Autowired
    private OperatorPwdHelper operatorPwdHelper;

    @Reference
    private EmployerOperatorFacade employerOperatorFacade;

    @Reference
    private EmployerStaffFacade employerStaffFacade;

    @Reference
    private EmployerRoleFacade employerRoleFacade;

    @Reference
    private MerchantQueryFacade merchantQueryFacade;

    @Reference
    private MerchantEmployerMainFacade merchantEmployerMainFacade;

    /**
     * 获取图形验证码
     * @return
     */
    @PostMapping("captcha")
    public RestResult<CaptchaVO> captcha() {
        return RestResult.success(captchaHelper.genCaptcha());
    }

    /**
     * 发送短信验证码
     * @return
     */
    @PostMapping("sendSmsCode")
    @Logger(type = OperateLogTypeEnum.LOGIN, action = "发送短信验证码")
    public RestResult<String> sendSmsCode(@RequestParam String phone) {
        captchaHelper.sendSmsCode(phone);
        return RestResult.success("短信验证码发送成功");
    }

    /**
     * 查询用户状态
     */
    @GetMapping("getUserStatus")
    @Logger(type = OperateLogTypeEnum.QUERY, action = "查询是否注册")
    public RestResult<Map<String, Object>> getUserStatus(@RequestParam String phone) {
        EmployerOperator operator = employerOperatorFacade.getByPhone(phone);

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("register", operator != null);
        resultMap.put("initPwd", operator != null && operator.getIsInitPwd() == PortalInitPwdStatusEnum.INITED.getValue());
        if (operator != null) {
            resultMap.put("status", operator.getStatus());
        }
        return RestResult.success(resultMap);
    }

    /**
     * 设置登录密码（使用手机验证码）
     * @return
     * 前端请求此接口前，需要先请求/getUserPublickey接口获取动态的公钥
     */
    @PostMapping("resetPwd")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "设置登录密码")
    public RestResult<String> resetPwd(@RequestParam String phone,
                                       @RequestParam String pwd,
                                       @RequestParam String smsCode,
                                       HttpServletRequest request) {
        EmployerOperator operator = employerOperatorFacade.getByPhone(phone);
        if (operator == null) {
            return RestResult.error("帐号未注册，请联系客服");
        }

        captchaHelper.verifySmsCode(phone, smsCode);

        // 密码解密
        //校验RSA公私钥对，同时返回私钥
        String privateKey = operatorPwdHelper.getPrivateKey(phone, request);
        pwd = dataDecryptHelper.decryptData(privateKey, pwd);
        operatorPwdHelper.updatePwd(operator, pwd, false, null);

        captchaHelper.invalidSmsCode(phone);
        return RestResult.success("设置密码成功");
    }

    /**
     * 修改密码（登录状态下）
     * @param originPwd     原密码，为加密后的
     * @param pwd           新密码，为加密后的
     */
    @PostMapping("updatePwd")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "修改密码")
    public RestResult<String> updatePwd(@RequestParam(required = false) String originPwd,
                                        @RequestParam String pwd,
                                        @CurrentOperatorVO EmployerOperatorVO operatorVO,
                                        @CurrentSession Session session,
                                        HttpServletRequest request) {
        EmployerOperator operator = employerOperatorFacade.getByPhone(operatorVO.getPhone());


        //此时登录状态，从session拿到私钥
        String privateKey = session.getAttribute(PermissionConstant.OPERATOR_SESSION_PRIVATEKEY_KEY, String.class);
        pwd = dataDecryptHelper.decryptData(privateKey, pwd);
        originPwd = dataDecryptHelper.decryptData(privateKey, originPwd);
        if (operator.getIsInitPwd() == PortalInitPwdStatusEnum.NO_INIT.getValue()) {  // 未初始化密码，直接设置
            operatorPwdHelper.updatePwd(operator, pwd, false, originPwd);
        } else {
            if (StringUtil.isEmpty(originPwd)) {
                return RestResult.error("原密码不能为空");
            }
            operatorPwdHelper.updatePwd(operator, pwd, true, originPwd);
        }

        // 退出登录
        sessionManager.deleteSession(session.getPhone(), session.getUuid());
        return RestResult.success("修改密码成功，请重新登录");
    }

    /**
     * 获取RSA公钥
     * @param phone
     * @return
     */
    @GetMapping("getUserPublickey")
    @Logger(type = OperateLogTypeEnum.LOGIN, action = "登录前获取RSA公钥")
    public RestResult<Map<String, Object>> getUserPublickey(@RequestParam String phone,
                                                            HttpServletRequest request) {
        Map<String, String> keyPair = RSAUtil.genKeyPair();
        //加多两个浏览器和ip信息由于校验
        keyPair.put("UserAgent", request.getHeader("User-Agent"));
        keyPair.put("Host", NetUtil.getIpAddr(request));
        operatorPwdHelper.cachePublicKey(phone, keyPair);

        Map<String, Object> result = new HashMap<>();
        result.put(RSAUtil.PUBLIC_KEY, keyPair.get(RSAUtil.PUBLIC_KEY));
        return RestResult.success(result);
    }

    /**
     * 登录
     * @return
     */
    @PostMapping("login")
    @Logger(type = OperateLogTypeEnum.LOGIN, action = "登录")
    public RestResult<LoginResVO> login(@RequestBody @Valid LoginReqVO loginReqVO,
                                        HttpServletRequest request) {
        // 校验登录参数
        verifyLoginParam(loginReqVO);

        // 密码校验
        EmployerOperator operator = verifyLogin(loginReqVO, request);

        EmployerOperatorVO vo = EmployerOperatorVO.build(operator);
        // 创建session
        Session session = sessionManager.createSession(vo.getPhone());
        session.save();
        session.setAttribute(PermissionConstant.OPERATOR_SESSION_KEY, JsonUtil.toString(vo));
        //登录成功，则把该用户的私钥交给session来管理
        session.setAttribute(PermissionConstant.OPERATOR_SESSION_PRIVATEKEY_KEY, operator.getPrivateKey());
        // 生成token
        JwtInfo jwtInfo = new JwtInfo();
        jwtInfo.setUuid(session.getUuid());
        jwtInfo.setEmployerOperatorVO(vo);
        // 返回信息
        LoginResVO loginResVO = new LoginResVO();
        loginResVO.setToken(jwtHelper.genToken(request, jwtInfo));
        loginResVO.setOperator(vo);
        loginResVO.setMchNos(getMchInfoListByPhone(loginReqVO.getPhone()));

        return RestResult.success(loginResVO);
    }

    /**
     * 校验登录参数
     */
    public void verifyLoginParam(LoginReqVO loginReqVO) throws BizException {
        if (loginReqVO.getLoginType() != PortalLoginTypeEnum.PWD.getValue()
                && loginReqVO.getLoginType() != PortalLoginTypeEnum.SMS.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("登录方式参数错误");
        }
        if (StringUtil.isEmpty(loginReqVO.getCaptcha()) || StringUtil.isEmpty(loginReqVO.getCaptchaId())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("图形验证码不能为空");
        }

        // 密码登录
        if (loginReqVO.getLoginType() == PortalLoginTypeEnum.PWD.getValue() && StringUtil.isEmpty(loginReqVO.getPwd())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("密码不能为空");
        }

        // 手机验证码登录
        if (loginReqVO.getLoginType() == PortalLoginTypeEnum.SMS.getValue() && StringUtil.isEmpty(loginReqVO.getSmsCode())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("手机验证码不能为空");
        }
    }

    /**
     * 验证登录
     */
    public EmployerOperator verifyLogin(LoginReqVO loginReqVO, HttpServletRequest request) throws BizException {
        EmployerOperator operator = employerOperatorFacade.getByPhone(loginReqVO.getPhone());
        if (operator == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("帐号或密码错误");
        } else if (operator.getStatus() == PortalOperatorStatusEnum.INACTIVE.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("帐号密码输入错误次数过多已被冻结，请重置密码后再登录");
        }

        // 校验图形验证码
        captchaHelper.verifyCaptcha(loginReqVO.getCaptchaId(), loginReqVO.getCaptcha());

        //校验RSA公私钥对，同时返回私钥
        String privateKey = operatorPwdHelper.getPrivateKey(loginReqVO.getPhone(), request);
        if (loginReqVO.getLoginType() == PortalLoginTypeEnum.PWD.getValue()) {  // 密码登录
            // 校验登录密码
            if (operator.getIsInitPwd() == PortalInitPwdStatusEnum.NO_INIT.getValue()) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("帐号未设置密码");
            }
            // 解密密码并校验
            operatorPwdHelper.loginVerifyPwd(operator, dataDecryptHelper.decryptData(privateKey, loginReqVO.getPwd()));
            //校验通过则临时把私钥放入
            operator.setPrivateKey(privateKey);
        } else if (loginReqVO.getLoginType() == PortalLoginTypeEnum.SMS.getValue()) {  // 手机验证码登录
            // 解密验证码 并校验校验手机验证码
            captchaHelper.verifySmsCode(loginReqVO.getPhone(), dataDecryptHelper.decryptData(privateKey, loginReqVO.getSmsCode()));
            captchaHelper.invalidSmsCode(loginReqVO.getPhone());
            //校验通过则临时把私钥放入
            operator.setPrivateKey(privateKey);
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("登录方式参数错误");
        }

        captchaHelper.invalidCaptcha(loginReqVO.getCaptchaId());  // 图形验证码失效
        return operator;
    }

    /**
     * 退出
     * @return
     */
    @GetMapping("logout")
    @Logger(type = OperateLogTypeEnum.LOGOUT, action = "登出")
    public RestResult<String> logout(@CurrentSession Session session) {
        sessionManager.deleteSession(session.getPhone(), session.getUuid());
        return RestResult.success("");
    }

    /**
     * 查询可选商户列表
     * @return
     */
    @GetMapping("mchList")
    public RestResult<List<MchInfoVO>> mchList(@CurrentOperatorVO EmployerOperatorVO operatorVO) {
        return RestResult.success(getMchInfoListByPhone(operatorVO.getPhone()));
    }

    /**
     * 选择商户
     * @return  权限标识
     */
    @GetMapping("selectMch")
    public RestResult<SelectMchResVO> selectMch(@RequestParam String mchNo,
                                                @CurrentOperatorVO EmployerOperatorVO operatorVO,
                                                @CurrentSession Session session) {
        EmployerStaffVO staffVO = employerStaffFacade.getByPhone(mchNo, operatorVO.getPhone());
        if (staffVO == null) {
            return RestResult.error("不是该商户的员工");
        }

        // 商户状态校验
        Merchant merchant = merchantQueryFacade.getByMchNo(mchNo);
        if (merchant == null) {
            return RestResult.error("商户不存在");
        }
        if (merchant.getMchStatus() == MchStatusEnum.INACTIVE.getValue()) {
            return RestResult.error("商户已被冻结");
        }

        List<FunctionVO> functions = null;
        if (employerStaffFacade.isAdmin(staffVO)) {
            functions = employerStaffFacade.listAllFunction().stream().map(FunctionVO::buildVo).collect(Collectors.toList());
        } else {
            functions = employerStaffFacade.listFunctionByStaffId(mchNo, staffVO.getId()).stream() .map(FunctionVO::buildVo).collect(Collectors.toList());
        }

        // 商户编号、员工信息、权限信息更新至session
        Map<String, String> map = new HashMap<>();
        map.put(PermissionConstant.MCH_NO_SESSION_KEY, mchNo);
        map.put(PermissionConstant.STAFF_SESSION_KEY, JsonUtil.toString(staffVO));
        map.put(PermissionConstant.PERMISSION_SESSION_KEY, JsonUtil.toString(
                functions.stream().map(FunctionVO::getPermissionFlag).collect(Collectors.toList())));
        session.setAttribute(map);
        // 返回
        SelectMchResVO selectMchResVO = new SelectMchResVO();
        selectMchResVO.setStaff(staffVO);
        selectMchResVO.setFunctions(functions);
        return RestResult.success(selectMchResVO);
    }

    /**
     * 修改个人信息
     */
    @PostMapping("updateInfo")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "修改个人信息")
    public RestResult<String> updateInfo(@RequestBody @Valid UpdateInfoVO updateInfoVO,
                                         @CurrentOperatorVO EmployerOperatorVO operatorVO,
                                         @CurrentSession Session session) {
        EmployerOperator operator = employerOperatorFacade.getByPhone(operatorVO.getPhone());
        operator.setName(updateInfoVO.getName());
        operator.setHeadPortraitFileUrl(updateInfoVO.getHeadPortraitFileUrl());
        employerOperatorFacade.update(operator);
        // 修改session中的信息
        operatorVO.setName(updateInfoVO.getName());
        session.setAttribute(PermissionConstant.OPERATOR_SESSION_KEY, JsonUtil.toString(operatorVO));
        return RestResult.success("操作成功");
    }

    /**
     * 获取登录信息
     */
    @RequestMapping("info")
    public RestResult<UserInfo> info(@CurrentSession Session session) {
        UserInfo userInfo = new UserInfo();
        userInfo.setOperator(session.getAttribute(PermissionConstant.OPERATOR_SESSION_KEY, EmployerOperatorVO.class));
        userInfo.setMchNo(session.getAttribute(PermissionConstant.MCH_NO_SESSION_KEY, String.class));
        EmployerStaffVO employerStaffVO = session.getAttribute(PermissionConstant.STAFF_SESSION_KEY, EmployerStaffVO.class);
        userInfo.setStaff(employerStaffVO);

        if (employerStaffVO == null || StringUtil.isEmpty(userInfo.getMchNo())) {
            userInfo.setFunctions(null);
            return RestResult.success(userInfo);
        }
        // 判断是否为超管
        boolean isAdmin = employerStaffFacade.isAdmin(employerStaffVO);
        if (isAdmin) {
            userInfo.setFunctions(employerStaffFacade.listAllFunction().stream().map(FunctionVO::buildVo).collect(Collectors.toList()));
        } else {
            userInfo.setFunctions(employerStaffFacade.listFunctionByStaffId(userInfo.getMchNo(), userInfo.getStaff().getId())
                    .stream().map(FunctionVO::buildVo).collect(Collectors.toList()));
        }
        return RestResult.success(userInfo);
    }

    /**
     * 查询操作员的可选商户列表
     * @return
     */
    public List<MchInfoVO> getMchInfoListByPhone(String phone) {
        List<String> mchNos = employerStaffFacade.listByPhone(phone).stream()
                .map(EmployerStaffVO::getMchNo).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(mchNos)){
            return new ArrayList<>();
        }
        // 查询操作员关联的商户
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mchNos", mchNos);
        paramMap.put("merchantType", MerchantTypeEnum.EMPLOYER.getValue());
        List<Merchant> merchants = merchantQueryFacade.listBy(paramMap);

        List<MchInfoVO> mchInfoVOS = merchants.stream().map(merchant -> {
            MchInfoVO mchInfoVO = new MchInfoVO();
            MerchantEmployerMain employerMain = merchantEmployerMainFacade.getByMchNo(merchant.getMchNo());
            if (employerMain!=null){
                mchInfoVO.setShortName(employerMain.getShortName());
            }
            mchInfoVO.setMchNo(merchant.getMchNo());
            mchInfoVO.setMchName(merchant.getMchName());
            mchInfoVO.setMchStatus(merchant.getMchStatus());
            mchInfoVO.setAuthStatus(merchant.getAuthStatus());
            mchInfoVO.setContactPhone(merchant.getContactPhone());
            return mchInfoVO;
        }).collect(Collectors.toList());
        return mchInfoVOS;
    }
}
