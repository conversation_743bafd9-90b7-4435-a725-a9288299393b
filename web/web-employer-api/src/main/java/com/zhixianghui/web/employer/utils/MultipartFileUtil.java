package com.zhixianghui.web.employer.utils;

import org.apache.commons.fileupload.disk.DiskFileItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.File;
import java.io.IOException;

public class MultipartFileUtil {
	
	private static final Logger log = LoggerFactory.getLogger(MultipartFileUtil.class);
	
	/**
	 * MultipartFile转File工具类
	 * @param multipartFile 文件
	 * @return
	 */
	public static File transfer2File(MultipartFile multipartFile) {
		
		File fileResult;
		//处理上传的文件
        CommonsMultipartFile cf = (CommonsMultipartFile)multipartFile;
        //这个file是MultipartFile的  
        DiskFileItem diskFileItem = (DiskFileItem) cf.getFileItem();
        File file = diskFileItem.getStoreLocation();  
        fileResult = file;
        //手动创建临时文件
		File tmpFile = new File(System.getProperty("java.io.tmpdir") + System.getProperty("file.separator") +
				file.getName());
		try {
			multipartFile.transferTo(tmpFile);
			fileResult = tmpFile;
		} catch (IllegalStateException | IOException e) {
			log.error("==>MultipartFileUtil MultipartFileUtil:", e);
		}
       return fileResult;
		
	}
	
	/**
     * 判断文件大小
     *
     * @param len
     *            文件长度
     * @param size
     *            限制大小
     * @param unit
     *            限制单位（B,K,M,G）
     * @return 小于返回true 大于返回false
     */
    public static boolean checkFileSize(Long len, int size, String unit) {
        double fileSize = 0;
		switch (unit.toUpperCase()) {
			case "B":
				fileSize = (double) len;
				break;
			case "K":
				fileSize = (double) len / 1024;
				break;
			case "M":
				fileSize = (double) len / 1048576;
				break;
			case "G":
				fileSize = (double) len / 1073741824;
				break;
			default:
				log.error("找不到对应单位");
		}
		return fileSize < size;
	}

}
