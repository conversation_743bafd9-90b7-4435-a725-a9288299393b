<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zhixianghui</groupId>
    <artifactId>zhixianghui-all</artifactId>
    <version>1.1-SNAPSHOT</version>
    <packaging>pom</packaging>



    <!-- 定义全局共享变量 -->
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>

        <!-- springBoot体系 -->
        <spring-boot.version>2.1.9.RELEASE</spring-boot.version>
        <spring-cloud.version>Greenwich.SR3</spring-cloud.version>
        <spring-cloud-alibaba.version>2.1.1.RELEASE</spring-cloud-alibaba.version>

        <!-- db相关 -->
        <pagehelper-starter.version>1.2.13</pagehelper-starter.version>
        <mybatis-starter.version>2.1.1</mybatis-starter.version>
        <mybatis.version>3.5.3</mybatis.version>
        <mybatis-spring.version>2.0.3</mybatis-spring.version>
        <druid-starter.version>1.2.8</druid-starter.version>
        <druid.version>1.2.8</druid.version>
        <mysql-connector.version>8.0.12</mysql-connector.version>
        <mybtais-plus.version>3.4.2</mybtais-plus.version>
        <!-- 中间件：MQ、helper、Zookeeper等 -->
        <activemq-spring.version>5.15.9</activemq-spring.version>
        <rocketmq-spring-boot.version>2.1.0.1</rocketmq-spring-boot.version>
        <rocketmq.version>4.6.1</rocketmq.version>
        <redisson.version>3.10.1</redisson.version>
        <redisson-starter.version>3.9.1</redisson-starter.version>
        <quartz.version>2.3.2</quartz.version>
        <curator.version>4.2.0</curator.version>

        <!-- dubbo 相关 -->
        <dubbo.version>2.7.3</dubbo.version>

        <!--fastdfs-client-->
        <fastdfs-client.version>1.27-SNAPSHOT</fastdfs-client.version>

        <!-- 其他starter -->
        <jasypt-starter.version>2.1.1</jasypt-starter.version>
        <easyexcel.version>2.2.6</easyexcel.version>

        <es-version>7.0.1</es-version>

        <!-- 基础工具相关 -->
        <servlet-api.version>4.0.1</servlet-api.version>
        <aspectjweaver.version>1.9.5</aspectjweaver.version>
        <commons-io.version>2.6</commons-io.version>
        <commons-codec.version>1.13</commons-codec.version>
        <commons-lang3.version>3.9</commons-lang3.version>
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <fastjson.version>1.2.83</fastjson.version>
        <java-jwt.version>3.8.3</java-jwt.version>
        <joda-time.version>2.10.2</joda-time.version>
        <okhttp.version>3.14.2</okhttp.version>
        <guava.version>19.0</guava.version>
        <bcprov.version>1.62</bcprov.version>
        <jedis.version>3.2.0</jedis.version>
        <perf4j.version>0.9.16</perf4j.version>
        <lombok.version>1.18.12</lombok.version>
        <swagger.version>3.0.0</swagger.version>

        <appName>${project.artifactId}</appName>
        <mavenBuildTime>${maven.build.timestamp}</mavenBuildTime>
    </properties>

    <!-- maven项目构建时的环境变量 -->
    <profiles>
        <!-- 开发环境 -->
        <profile>
            <id>dev</id>
            <properties>
                <env>dev</env>
                <!-- nexus私有仓库的地址 -->
                <privateRepoAddress>http://************:8081</privateRepoAddress>
                <projectVersion>1.0-SNAPSHOT</projectVersion>
                <logHome>/home/<USER>/logs/dev</logHome>
                <nacosAddr>************:8848</nacosAddr>
                <nacosNamespace>fdf25871-d673-4da2-9b22-c2ea987f9523</nacosNamespace>
            </properties>
        </profile>

        <profile>
            <id>pht</id>
            <properties>
                <env>pht</env>
                <!-- nexus私有仓库的地址 -->
                <privateRepoAddress>http://************:8081</privateRepoAddress>
                <projectVersion>1.0-SNAPSHOT</projectVersion>
                <logHome>D:/workspace/huijuzhixiang/new/dev/logs/dev</logHome>
                <nacosAddr>************:8848</nacosAddr>
                <nacosNamespace>7dad5f0b-366b-4c88-9597-37902d51d73f</nacosNamespace>
            </properties>
        </profile>

        <profile>
            <id>pht-local-test</id>
            <properties>
                <env>pht-local-test</env>
                <!-- nexus私有仓库的地址 -->
                <privateRepoAddress>http://************:8081</privateRepoAddress>
                <projectVersion>1.0-SNAPSHOT</projectVersion>
                <logHome>C:/Users/<USER>/workspace/huijuzhixiang/new/dev/logs/dev</logHome>
                <nacosAddr>127.0.0.1:8848</nacosAddr>
                <nacosNamespace>2d266c1e-88d4-407a-ad1c-39c493346878</nacosNamespace>
            </properties>
        </profile>

        <!-- 测试环境 -->
        <profile>
            <id>test</id>
            <properties>
                <env>test</env>
                <!-- nexus私有仓库的地址 -->
                <privateRepoAddress>http://************:8081</privateRepoAddress>
                <projectVersion>1.0-Beta</projectVersion>
                <logHome>/home/<USER>/logs/test</logHome>
                <nacosAddr>************:8850,************:8851,************:8852</nacosAddr>
                <nacosNamespace>0ec2d472-dbcd-47e2-9f92-40fe3cc75d47</nacosNamespace>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>

        <profile>
            <id>prod</id>
            <properties>
                <env>prod</env>
                <!-- nexus私有仓库的地址 -->
                <projectVersion>1.0-Release</projectVersion>
                <privateRepoAddress>http://************:8081</privateRepoAddress>
                <logHome>/home/<USER>/logs</logHome>
                <nacosAddr>http://nacos.hjzx-ext:8848</nacosAddr>
            </properties>
        </profile>
    </profiles>

    <!-- maven项目构建时的对于资源和插件的声明和管理 -->
    <build>
        <defaultGoal>compile</defaultGoal>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <compilerVersion>${java.version}</compilerVersion>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>

            <!--打包跳过测试的插件-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>

            <!-- 打包时不打到jar包中的文件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.2</version>
                <configuration>
                    <excludes>
                        <exclude>*.sh</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <!-- 资源文件处理 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <!-- 在执行package时把编译好的 *.sh 文件拷贝到项目构建目录下 -->
                    <execution>
                        <id>copy-command</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>${project.build.directory}/classes</directory>
                                    <includes>
                                        <include>**.sh</include>
                                    </includes>
                                </resource>
                            </resources>

                            <!-- 打包时不使用filter的文件(即单纯的复制文件过去) -->
                            <nonFilteredFileExtensions>
                                <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                                <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                                <nonFilteredFileExtension>doc</nonFilteredFileExtension>
                                <nonFilteredFileExtension>docx</nonFilteredFileExtension>
                                <nonFilteredFileExtension>pdf</nonFilteredFileExtension>
                            </nonFilteredFileExtensions>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- 引用插件，配置时间的时区和格式 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <id>timestamp-property</id>
                        <goals>
                            <goal>timestamp-property</goal>
                        </goals>
                        <configuration>
                            <name>buildTime</name><!-- 可被引用的变量 -->
                            <pattern>yyyy-MM-dd HH:mm:ss</pattern>
                            <locale>zh_CN</locale>
                            <timeZone>GMT+8</timeZone>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <filtering>true</filtering><!-- 声明用pom中定义的变量替换资源文件中 @...@ 里面的值 -->
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>

        <testResources>
            <testResource>
                <directory>src/test/java</directory>
            </testResource>
        </testResources>
    </build>

    <!-- 项目发布的私服 -->
    <distributionManagement>
        <repository><!-- 正式版本的发布地址 -->
            <id>maven-releases</id>
            <name>Release Repository</name>
            <url>${privateRepoAddress}/repository/maven-releases/</url>
        </repository>

        <snapshotRepository><!-- 快照版本的发布地址 -->
            <id>maven-snapshots</id>
            <name>Snapshot Repository</name>
            <url>${privateRepoAddress}/repository/maven-snapshots/</url>
            <uniqueVersion>false</uniqueVersion>
        </snapshotRepository>
    </distributionManagement>

    <!-- 依赖项目的声明和管理 -->
    <dependencyManagement>
        <dependencies>
            <!-- ################################# 基础依赖配置 START #################################### -->
            <!-- spring boot：自定义parent pom时使用spring boot的方式 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency><!-- Spring Boot的核心启动器，包含了自动配置、日志和YAML -->
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions><!-- 去掉默认的日志配置 -->
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- ################################# 基础依赖配置 END #################################### -->


            <!-- ################################# 第三方组件 START ################################# -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring-boot.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-devtools</artifactId>
                <version>${spring-boot.version}</version>
                <optional>true</optional>
            </dependency>

            <!-- Dubbo  -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo</artifactId>
                <version>${dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-dependencies-zookeeper</artifactId>
                <version>${dubbo.version}</version>
                <type>pom</type>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- curator -->
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${curator.version}</version>
            </dependency>

            <!-- mybatis -->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis-spring.version}</version>
            </dependency>
            <dependency><!--Mybatis 分页插件 pagehelper -->
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-autoconfigure</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus</artifactId>
                <version>${mybtais-plus.version}</version>
            </dependency>
            <!-- quartz -->
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>${quartz.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>

            <!-- RocketMQ -->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq-spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${es-version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${es-version}</version>
            </dependency>
            <!-- ################################# 第三方组件 END ################################# -->


            <!-- ################################# 第三方工具包 START ################################# -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons-beanutils.version}</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda-time.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>${servlet-api.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${java-jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency><!--RSA算法套件提供者-->
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>${bcprov.version}</version>
            </dependency>
            <dependency>
                <groupId>org.perf4j</groupId>
                <artifactId>perf4j</artifactId>
                <version>${perf4j.version}</version>
            </dependency>

            <!-- ################################# 第三方工具包 END ################################# -->

            <!-- ##################### common start ###################-->
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>common-util</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>common-statics</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>common-shardingjdbc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>common-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>component-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>api-base</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>generic-service-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>leaf-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- ##################### facade start ###################-->
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>facade-account-invoice</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>facade-banklink</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>facade-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>facade-data</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>facade-export</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>facade-fee</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>facade-flow</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>facade-merchant</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>facade-employee</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>facade-notify</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>facade-risk-control</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>facade-timer</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>facade-trade</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>facade-employee</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhixianghui</groupId>
                <artifactId>facade-pay</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!-- 需要全局引入的依赖jar START -->
    <dependencies>
        <dependency> <!-- 引入log4j2依赖，代表使用log4j2 -->
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>

        <!--skywalking集成log4j-->
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-log4j-2.x</artifactId>
            <version>8.1.0</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>

    </dependencies>
    <!-- 需要全局引入的依赖jar END -->

    <modules>
        <module>common</module>
        <module>starter</module>
        <module>middleware</module>
        <module>facade</module>
        <module>service</module>
        <module>api</module>
        <module>web</module>
    </modules>
</project>
